/**
 * @file log_writer_tcp.h
 * @brief
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2023-11-21
 *
 * @copyright Copyright (c) 2023  深圳爱夫迪软件技术有限公司
 *
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2023-11-21 <td>1.0     <td>jermia     <td>内容
 * </table>
 */

#pragma once

#include <px4_defines.h>
#include <stdint.h>
#include <pthread.h>
#include <drivers/drv_hrt.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <sys/types.h>
#include <netinet/in.h>
#include <sys/socket.h>
#include <netdb.h>
#include <perf/perf_counter.h>
#include <librdkafka/rdkafkacpp.h>
#include "binary_stream.pb.h"
#include "kafka_config.h"
#include <vector>
#include <mutex>

namespace px4
{
    namespace logger
    {
        /**
         * @class LogWriterTCP
         * Writes logging data to a file
         */
        class LogWriterTCP
        {
          public:
            LogWriterTCP(size_t buffer_size, const char *ip, const char *port);
            ~LogWriterTCP();

            bool init();

            /**
             * start the thread
             * @return 0 on success, error number otherwise (@see pthread_create)
             */
            int thread_start();

            void thread_stop();

            void start_log(const char *filename);

            void stop_log();

            bool is_started() const
            {
                return _buffers._should_run;
            }

            /** @see LogWriter::write_message() */
            int write_message(void *ptr, size_t size, uint64_t dropout_start = 0);

            void lock()
            {
                pthread_mutex_lock(&_mtx);
            }

            void unlock()
            {
                pthread_mutex_unlock(&_mtx);
            }

            void notify()
            {
                pthread_cond_broadcast(&_cv);
            }

            size_t get_total_written() const
            {
                return _buffers.total_written();
            }

            size_t get_buffer_size() const
            {
                return _buffers.buffer_size();
            }

            size_t get_buffer_fill_count() const
            {
                return _buffers.count();
            }

            void set_need_reliable_transfer(bool need_reliable)
            {
                _need_reliable_transfer = need_reliable;
            }

            bool need_reliable_transfer() const
            {
                return _need_reliable_transfer;
            }

            pthread_t thread_id() const
            {
                return _thread;
            }

          private:
            static void *run_helper(void *);

            void run();

            /**
             * write w/o waiting/blocking
             */
            int write(void *ptr, size_t size, uint64_t dropout_start);

            /* 512 didn't seem to work properly, 4096 should match the FAT cluster size */
            static constexpr size_t _min_write_chunk = 4096;

            /**
             * @struct BatchedMessage
             * 批处理消息结构，用于累积多个小消息
             */
            struct BatchedMessage
            {
                std::vector<uint8_t> data;
                size_t original_size;
                uint32_t checksum;

                BatchedMessage(const void *ptr, size_t size, uint32_t cs)
                    : original_size(size), checksum(cs)
                {
                    data.resize(size);
                    memcpy(data.data(), ptr, size);
                }
            };

            class LogFileBuffer
            {
              public:
                LogFileBuffer(size_t log_buffer_size, perf_counter_t perf_write, perf_counter_t perf_fsync, const char *ip,
                              const char *port);

                ~LogFileBuffer();

                bool start_log(const char *filename);

                void close_file();

                size_t get_read_ptr(void **ptr, bool *is_part);

                /**
                 * Write to the buffer but assuming there is enough space
                 */
                inline void write_no_check(void *ptr, size_t size);

                size_t available() const
                {
                    return _buffer_size - _count;
                }

                int fd() const
                {
                    return _fd;
                }

                inline ssize_t write_to_file(const void *buffer, size_t size, bool call_fsync) const;

                inline void fsync() const;

                /**
                 * 批处理相关方法
                 */
                void add_to_batch(const void *buffer, size_t size);
                void flush_batch(bool force = false);
                bool should_flush_batch() const;

                void mark_read(size_t n)
                {
                    _count -= n;
                    _total_written += n;
                }

                size_t total_written() const
                {
                    return _total_written;
                }
                size_t buffer_size() const
                {
                    return _buffer_size;
                }
                size_t count() const
                {
                    return _count;
                }

                bool _should_run = false;
                int mysocket_;

                std::string _chip_id;
                std::string _datetime;
                RdKafka::Producer *_producer;
                RdKafka::Conf *_conf;
                std::string _topic_name;
                int32_t _partition;
                mutable uint64_t _seq_num;

              private:
                const size_t _buffer_size;
                int _fd = -1;
                uint8_t *_buffer = nullptr;
                size_t _head = 0; ///< next position to write to
                size_t _count = 0; ///< number of bytes in _buffer to be written
                size_t _total_written = 0;
                perf_counter_t _perf_write;
                perf_counter_t _perf_fsync;
                const char *_server_ip{nullptr};
                const char *_server_port{nullptr};

                // 批处理相关成员变量
                std::vector<BatchedMessage> _batch_messages;
                size_t _batch_total_size;
                hrt_abstime _batch_start_time;
                mutable std::mutex _batch_mutex;
            };

            LogFileBuffer _buffers;

            bool        _exit_thread = false;
            bool        _need_reliable_transfer = false;
            pthread_mutex_t     _mtx;
            pthread_cond_t      _cv;
            pthread_t _thread = 0;
        };

    }
}
