# Kafka消息批处理优化方案

## 问题描述

原始实现中，每个约100字节的小消息都被单独包装成protobuf消息并立即发送到Kafka，导致：

1. **网络开销大**：每个小消息都有独立的protobuf元数据开销
2. **Kafka效率低**：大量小消息导致网络传输和处理效率低下
3. **消息数量多**：原本可以合并的数据被拆分成大量独立消息

## 优化方案

### 核心思路
在不修改protobuf消息结构的前提下，通过**消息批处理**来优化：
- 累积多个小消息的payload
- 将多个payload合并成一个大的payload
- 用单个protobuf消息发送合并后的数据

### 实现细节

#### 1. 配置参数 (kafka_config.h)
```cpp
// 消息批处理缓冲区大小(字节)
#define MESSAGE_BATCH_BUFFER_SIZE 32768

// 批处理触发阈值(字节) - 达到此大小时触发发送
#define BATCH_TRIGGER_SIZE 16384

// 批处理超时时间(毫秒) - 超过此时间强制发送
#define BATCH_TIMEOUT_MS 100

// 最大批处理消息数量
#define MAX_BATCH_MESSAGE_COUNT 50

// 优化的Kafka配置
#define LINGER_MS_CONFIG 50        // 增加延迟时间以积累更多消息
#define BATCH_SIZE_CONFIG 65536    // 增加批量大小
```

#### 2. 数据结构 (log_writer_tcp.h)
```cpp
struct BatchedMessage {
    std::vector<uint8_t> data;     // 消息数据
    size_t original_size;          // 原始大小
    uint32_t checksum;             // 校验和
};

// 批处理相关成员变量
std::vector<BatchedMessage> _batch_messages;  // 批处理消息缓冲区
size_t _batch_total_size;                     // 累积的总大小
hrt_abstime _batch_start_time;                // 批处理开始时间
mutable std::mutex _batch_mutex;              // 线程安全锁
```

#### 3. 核心方法

**添加消息到批处理缓冲区**
```cpp
void add_to_batch(const void *buffer, size_t size);
```

**检查是否需要刷新批处理缓冲区**
```cpp
bool should_flush_batch() const;
```
触发条件：
- 累积大小 >= BATCH_TRIGGER_SIZE (16KB)
- 消息数量 >= MAX_BATCH_MESSAGE_COUNT (50条)
- 时间超过 BATCH_TIMEOUT_MS (100ms)

**刷新批处理缓冲区**
```cpp
void flush_batch(bool force = false);
```
- 合并所有消息的payload
- 创建单个protobuf消息
- 发送到Kafka

#### 4. 修改的核心流程

**原始流程：**
```
小消息 -> 立即创建protobuf -> 立即发送到Kafka
```

**优化后流程：**
```
小消息 -> 添加到批处理缓冲区 -> 检查触发条件 -> 合并payload -> 创建单个protobuf -> 发送到Kafka
```

### 关键改进点

#### 1. 日志重启处理
在收到"重启日志记录"时：
- `close_file()` 方法调用 `flush_batch(true)` 强制发送剩余消息
- 重置批处理缓冲区状态
- 确保所有数据都被发送完毕

#### 2. 线程安全
- 使用 `std::mutex` 保护批处理缓冲区
- 所有批处理操作都是线程安全的

#### 3. 定期刷新
- 主循环中定期检查 `should_flush_batch()`
- 确保即使没有新消息，超时的批处理也会被发送

#### 4. 状态重置
- 在 `start_log()` 和 `close_file()` 中重置批处理状态
- 确保每次日志会话都有干净的批处理状态

## 预期效果

### 网络效率提升
- **消息数量减少**：50个小消息合并为1个大消息，减少98%的消息数量
- **元数据开销减少**：protobuf元数据从50份减少到1份
- **网络传输效率**：更大的消息包更适合网络传输

### 示例计算
假设50个100字节的消息：
- **原始方式**：50 × (100字节数据 + 50字节protobuf元数据) = 7500字节
- **批处理方式**：5000字节数据 + 50字节protobuf元数据 = 5050字节
- **节省**：约33%的网络传输量

### Kafka处理效率
- 减少Kafka broker的消息处理负载
- 提高消息吞吐量
- 减少网络连接开销

## 兼容性

- **接收端兼容**：接收端仍然收到标准的protobuf消息
- **数据完整性**：所有原始数据都被保留，只是打包方式不同
- **校验和**：对合并后的数据计算校验和，确保数据完整性

## 配置建议

根据实际情况调整以下参数：
- `BATCH_TRIGGER_SIZE`：根据网络带宽和延迟要求调整
- `BATCH_TIMEOUT_MS`：根据实时性要求调整
- `MAX_BATCH_MESSAGE_COUNT`：根据内存使用情况调整
