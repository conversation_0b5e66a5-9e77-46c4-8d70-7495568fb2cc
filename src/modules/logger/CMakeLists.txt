############################################################################
#
#   Copyright (c) 2016 PX4 Development Team. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
#
# 1. Redistributions of source code must retain the above copyright
#    notice, this list of conditions and the following disclaimer.
# 2. Redistributions in binary form must reproduce the above copyright
#    notice, this list of conditions and the following disclaimer in
#    the documentation and/or other materials provided with the
#    distribution.
# 3. Neither the name PX4 nor the names of its contributors may be
#    used to endorse or promote products derived from this software
#    without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
# "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
# LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
# FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
# COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
# INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
# BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS
# OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
# AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
# LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
# ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
# POSSIBILITY OF SUCH DAMAGE.
#
############################################################################

# 查找Protobuf包

# 设置包含路径和库名称
if ("${PX4_BOARD}" MATCHES "sitl")
  # SITL模式使用系统安装的protobuf库
  find_package(Protobuf 3.0.0 REQUIRED)
  set(PROTOBUF_PROTOC_EXECUTABLE /usr/local/bin/protoc)
else()
  # rk3308模式使用自定义路径的protobuf库
  set(PROTOBUF_INCLUDE_DIR ${gundam_base}/include)
  set(PROTOBUF_LIBRARIES libprotobuf.a)
  set(OPENSSL_INCLUDE_DIR ${gundam_base}/include)
  set(PROTOBUF_PROTOC_EXECUTABLE /usr/local/bin/protoc)
endif()

set(LIBRDKAFKA_INCLUDE_DIR ${gundam_base}/include/librdkafka)
set(LIBRDKAFKA_CPP_LIBRARIES librdkafka++.a)
set(LIBRDKAFKA_C_LIBRARIES librdkafka.a)

# 添加包含路径
if ("${PX4_BOARD}" MATCHES "sitl")
  include_directories(${PROTOBUF_INCLUDE_DIRS} ${LIBRDKAFKA_INCLUDE_DIR})
else()
  include_directories(${PROTOBUF_INCLUDE_DIR} ${LIBRDKAFKA_INCLUDE_DIR})
endif()

# 定义proto文件及生成的文件
set(PROTO_FILE ${CMAKE_CURRENT_SOURCE_DIR}/binary_stream.proto)
set(PROTO_SRC ${CMAKE_CURRENT_BINARY_DIR}/binary_stream.pb.cc)
set(PROTO_HDR ${CMAKE_CURRENT_BINARY_DIR}/binary_stream.pb.h)

# 添加生成protobuf代码的自定义命令
add_custom_command(
    OUTPUT ${PROTO_SRC} ${PROTO_HDR}
    COMMAND ${PROTOBUF_PROTOC_EXECUTABLE}
    ARGS --cpp_out=${CMAKE_CURRENT_BINARY_DIR} 
         --proto_path=${CMAKE_CURRENT_SOURCE_DIR} 
         ${PROTO_FILE}
    DEPENDS ${PROTO_FILE}
    COMMENT "Generating protobuf code from ${PROTO_FILE}"
    VERBATIM
)

# 创建一个自定义目标，确保在其他编译之前生成protobuf代码
add_custom_target(generate_proto_logger ALL DEPENDS ${PROTO_SRC} ${PROTO_HDR})

# 设置包含路径
include_directories(
  ${CMAKE_CURRENT_SOURCE_DIR}/include/DeviceIo
  ${CMAKE_CURRENT_BINARY_DIR}  # 添加生成的protobuf头文件路径
)

message(STATUS "logger register")
px4_add_module(
	MODULE modules__logger
	MAIN logger
	PRIORITY "SCHED_PRIORITY_MAX-30"
	COMPILE_FLAGS
		-Wno-cast-align -Wno-sign-compare -Wno-unused-parameter 
    -Wno-shadow -Wno-double-promotion -Wno-float-equal 
    -Wno-deprecated-declarations # TODO: fix and enable
	SRCS
		logger.cpp
		log_writer.cpp
		log_writer_file.cpp
		log_writer_tcp.cpp
		log_writer_mavlink.cpp
		common.cpp
		util.cpp
		watchdog.cpp
		${PROTO_SRC}  # 添加生成的protobuf源文件
	DEPENDS
		version
		generate_proto_logger  # 添加对protobuf生成的依赖
	)

if ("${PX4_BOARD}" MATCHES "sitl")
message(STATUS "logger sitl compile")
# 针对SITL，我们使用系统库
target_link_libraries(modules__logger PRIVATE 
    ${gundam_base}/lib/${LIBRDKAFKA_CPP_LIBRARIES} 
    ${gundam_base}/lib/${LIBRDKAFKA_C_LIBRARIES} 
    -lcurl -lzstd -llz4 -lsasl2
    -lssl -lcrypto -lz -lpthread -ldl
    ${PROTOBUF_LIBRARIES})
elseif ("${PX4_BOARD}" MATCHES "rk3308")
# 修改链接库顺序，确保正确的依赖关系

target_link_libraries(modules__logger PRIVATE 
    -lDeviceIo -lasound 
    #  ${gundam_base}/lib/libcurl.so.4.8.0
    # ${gundam_base}/lib/libssl.so.1.1
    # ${gundam_base}/lib/libcrypto.so.1.1
    ${gundam_base}/lib/${LIBRDKAFKA_CPP_LIBRARIES} 
    ${gundam_base}/lib/${LIBRDKAFKA_C_LIBRARIES} 
    -lz -lpthread -ldl -lcrypto
    ${gundam_base}/lib/${PROTOBUF_LIBRARIES})
endif()
