/**
 * @file log_writer_tcp.cpp
 * @brief
 * <AUTHOR> (cm<PERSON><EMAIL>)
 * @version 1.0
 * @date 2023-11-21
 *
 * @copyright Copyright (c) 2023  深圳爱夫迪软件技术有限公司
 *
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2023-11-21 <td>1.0     <td>jermia     <td>内容
 * </table>
 */

// 处理可能的宏定义冲突
#ifdef err
#define PX4_ERR_MACRO_ORIG err
#undef err
#endif

#include "log_writer_tcp.h"

// 恢复原始宏定义
#ifdef PX4_ERR_MACRO_ORIG
#define err PX4_ERR_MACRO_ORIG
#undef PX4_ERR_MACRO_ORIG
#endif

#include "messages.h"
#if defined(__PX4_POSIX_RK3308)
#include <DeviceIo/Rk_system.h>
#endif
#include "version.h"
#include <fcntl.h>
#include <string.h>
#include <errno.h>
#include <sys/time.h>
#include <sys/stat.h>
#include <mathlib/mathlib.h>
#include <px4_posix.h>
#include <openssl/md5.h>
#include <openssl/sha.h>
#include <iomanip>
#include <sstream>
#include <fstream>
#include <unordered_map>
#include <mutex>
#include <random>
#ifdef __PX4_NUTTX
#include <systemlib/hardfault_log.h>
#endif /* __PX4_NUTTX */
#include "common.h"
extern "C"
{
#include <sys_api.h>
}
// #define  DEBUG_TCP_LOCAL_FILE

// 添加MD相关函数
static MD5_CTX md5_context;
static bool md5_initialized = false;

// 初始化MD5上下文
static void init_md5_context()
{
    MD5_Init(&md5_context);
    md5_initialized = true;
}

// 更新MD5计算
static void update_md5_context(const uint8_t *data, size_t data_length)
{
    if(!md5_initialized)
    {
        init_md5_context();
    }

    MD5_Update(&md5_context, data, data_length);
}

// 完成MD5计算并返回结果
static std::string finalize_md5_context()
{
    unsigned char digest[MD5_DIGEST_LENGTH];
    MD5_Final(digest, &md5_context);
    std::stringstream ss;

    for(int i = 0; i < MD5_DIGEST_LENGTH; i++)
    {
        ss << std::hex << std::setw(2) << std::setfill('0') << (int)digest[i];
    }

    // 重置状态
    md5_initialized = false;
    return ss.str();
}
// SITL模式下生成机器唯一ID的辅助函数
#if !defined(__PX4_POSIX_RK3308)
#include <sys/types.h>
#include <sys/stat.h>
#include <ifaddrs.h>
#include <netinet/in.h>
#include <string.h>
#include <unistd.h>
#include <net/if.h>
#include <sys/socket.h>
#include <netdb.h>
#include <arpa/inet.h>
#include <sys/ioctl.h>
#include <fstream>

// 生成16字符长度的唯一ID
static std::string generate_unique_id()
{
    // 先检查缓存文件是否存在
    const char *home_dir = getenv("HOME");

    if(!home_dir)
    {
        home_dir = "/tmp";
    }

    char cache_path[512];
    snprintf(cache_path, sizeof(cache_path), "%s/.px4_machine_id", home_dir);
    // 尝试从缓存读取
    std::ifstream cache_file(cache_path);
    std::string cached_id;

    if(cache_file.good())
    {
        std::getline(cache_file, cached_id);

        if(cached_id.length() == 16)
        {
            PX4_INFO("使用缓存的机器ID: %s", cached_id.c_str());
            return cached_id;
        }
    }

    // 如果没有缓存或缓存无效，生成新ID
    std::string mac_addr;
    std::string hostname_str;
    // 1. 获取MAC地址 - 最稳定的硬件标识符
    struct ifaddrs *ifaddr, *ifa;
    bool mac_found = false;

    if(getifaddrs(&ifaddr) != -1)
    {
        for(ifa = ifaddr; ifa != NULL && !mac_found; ifa = ifa->ifa_next)
        {
            if(ifa->ifa_addr == NULL)
            {
                continue;
            }

            // 只考虑以太网和无线网络接口，排除回环
            if(!(ifa->ifa_flags & IFF_LOOPBACK) &&
                    (ifa->ifa_flags & IFF_UP))
            {
                struct ifreq ifr;
                int fd = socket(AF_INET, SOCK_DGRAM, 0);
                strcpy(ifr.ifr_name, ifa->ifa_name);

                if(ioctl(fd, SIOCGIFHWADDR, &ifr) != -1)
                {
                    // 只使用MAC地址的前6个字节
                    for(int i = 0; i < 6; i++)
                    {
                        char hex[3];
                        snprintf(hex, sizeof(hex), "%02x", (unsigned char)ifr.ifr_hwaddr.sa_data[i]);
                        mac_addr += hex;
                    }

                    mac_found = true;
                }

                close(fd);
            }
        }

        freeifaddrs(ifaddr);
    }

    // 2. 获取主机名
    char hostname[256] = {0};

    if(gethostname(hostname, sizeof(hostname)) == 0)
    {
        hostname_str = hostname;
    }

    // 3. 结合MAC地址和主机名生成散列值
    std::string combined = mac_addr + hostname_str;

    if(combined.empty())
    {
        // 如果无法获取有效的MAC和主机名，使用备用方法
        struct stat st;
        stat("/", &st); // 获取根目录的inode信息
        char buffer[128];
        snprintf(buffer, sizeof(buffer), "%lu-%lu",
                 (unsigned long)st.st_dev, (unsigned long)st.st_ino);
        combined = buffer;
    }

    // 使用简单哈希算法生成16位十六进制字符
    std::hash<std::string> hasher;
    size_t hash_value = hasher(combined);
    // 添加第二级散列以增加唯一性，但不使用动态变化的值如PID
    std::hash<size_t> size_hasher;
    size_t secondary_hash = size_hasher(hash_value);
    char id_buffer[17];
    snprintf(id_buffer, sizeof(id_buffer), "%08lx%08lx",
             (unsigned long)(hash_value & 0xFFFFFFFF),
             (unsigned long)(secondary_hash & 0xFFFFFFFF));
    std::string result = id_buffer;

    // 确保长度为16个字符
    if(result.length() > 16)
    {
        result = result.substr(0, 16);
    }
    else if(result.length() < 16)
    {
        result.append(16 - result.length(), '0');
    }

    // 将生成的ID保存到缓存
    std::ofstream out_cache(cache_path);

    if(out_cache.good())
    {
        out_cache << result;
        out_cache.close();
        PX4_INFO("已缓存机器ID: %s 到 %s", result.c_str(), cache_path);
    }

    return result;
}
#endif

using namespace time_literals;
namespace px4
{
    namespace logger
    {
        constexpr size_t LogWriterTCP::_min_write_chunk;
        static uint16_t send_count = 0;
        static uint64_t total_bytes_sent = 0;
        static uint64_t total_packets_sent = 0;
        static uint64_t failed_packets = 0;
        static uint32_t consecutive_failures = 0;
        static hrt_abstime last_success_time = 0;
        static uint32_t throttle_factor = 1;
        static constexpr uint32_t MAX_THROTTLE_FACTOR = 10;
        static constexpr uint32_t FAILURE_THRESHOLD = 500;

        // Kafka消息传递回调，处理消息发送成功或失败事件
        class DeliveryReportCallback : public RdKafka::DeliveryReportCb
        {
          public:
            DeliveryReportCallback() : success_count(0), error_count(0) {}

            void dr_cb(RdKafka::Message &message) override
            {
                if(message.err())
                {
                    PX4_ERR("消息发送失败 [%zu]: %s 主题:%s 分区:%d 时间戳：%ld",
                            error_count + success_count,
                            message.errstr().c_str(),
                            !message.topic_name().empty() ? message.topic_name().c_str() : "N/A",
                            message.partition(), message.timestamp().timestamp);
                    error_count++;
                    failed_packets++;
                    consecutive_failures++;

                    if(consecutive_failures > FAILURE_THRESHOLD && throttle_factor < MAX_THROTTLE_FACTOR)
                    {
                        throttle_factor++;
                        PX4_WARN("检测到网络问题，降低发送速率至 %d%%", 100 / throttle_factor);
                    }
                }
                else
                {
                    consecutive_failures = 0;
                    last_success_time = hrt_absolute_time();

                    if(throttle_factor > 1 && (success_count % 200 == 0))
                    {
                        throttle_factor--;
                        PX4_INFO("网络连接改善，提高发送速率至 %d%%", 100 / throttle_factor);
                    }

                    if(success_count % 500 == 0)
                    {
                        PX4_INFO("消息发送状态: 成功=%zu条, 失败=%zu条, 成功率=%.1f%%",
                                 success_count, error_count,
                                 (double)((float)success_count / (success_count + error_count + 1) * 100.0f));
                    }

                    success_count++;
                }
            }

            uint64_t success_count;
            uint64_t error_count;
        };

        // 添加主题索引和分区计算的缓存
        static std::unordered_map<std::string, int> topic_cache;
        static std::mutex cache_mutex;

        /**
         * 计算芯片ID对应的主题索引
         * 使用SHA-256哈希算法确保分布均匀
         */
        static int get_topic_index_for_chip_id(const std::string &chip_id)
        {
            // 先检查缓存
            {
                std::lock_guard<std::mutex> lock(cache_mutex);
                auto it = topic_cache.find(chip_id);

                if(it != topic_cache.end())
                {
                    return it->second;
                }
            }
            // 使用SHA-256哈希算法
            unsigned char hash[SHA256_DIGEST_LENGTH];
            SHA256(reinterpret_cast<const unsigned char *>(chip_id.data()),
                   chip_id.size(), hash);
            // 将所有32字节的哈希值异或合并成一个整数
            uint64_t hash_value = 0;

            for(int i = 0; i < SHA256_DIGEST_LENGTH; i++)
            {
                hash_value ^= (static_cast<uint64_t>(hash[i]) << ((i % 8) * 8));
            }

            int topic_index = static_cast<int>(hash_value % TOPIC_COUNT);
            // 存入缓存
            {
                std::lock_guard<std::mutex> lock(cache_mutex);
                topic_cache[chip_id] = topic_index;
            }
            return topic_index;
        }

        /**
         * 获取芯片ID对应的主题名称
         * 替换原有实现，使用新的哈希算法
         */
        std::string get_topic_for_chip_id(const std::string &chip_id)
        {
            int topic_index = get_topic_index_for_chip_id(chip_id);
            char suffix[3];
            snprintf(suffix, sizeof(suffix), "%02x", topic_index);
            return TOPIC_PREFIX + std::string(suffix);
        }

        /**
         * 根据芯片ID计算分区
         * 使用二次哈希确保均匀分布
         */
        int32_t get_partition_for_chip_id(const std::string &chip_id)
        {
            // 使用标准库的哈希函数对整个chip_id进行第二次哈希
            std::hash<std::string> hasher;
            size_t hash_value = hasher(chip_id);
            // 取模得到分区号，范围是0到PARTITION_COUNT-1
            return static_cast<int32_t>((hash_value / TOPIC_COUNT) % PARTITION_COUNT);
        }

        /**
         * 发送设备信息到fd_info主题
         * 在日志关闭时调用
         */
        static bool send_device_info(RdKafka::Producer *producer, const std::string &chip_id,
                                     const std::string &datetime, uint64_t log_file_size,
                                     const std::string &file_checksum)
        {
            if(!producer)
            {
                PX4_ERR("无法发送设备信息:producer为空");
                return false;
            }

            PX4_WARN("正在发送设备信息到fd_info主题...");
            // 创建设备信息
            binary_stream::DeviceInfo device_info;
            device_info.set_chip_id(chip_id);
            // 设置系统信息
#if defined(__PX4_POSIX_RK3308)
            device_info.set_machine_model("RK3308-ZAKU");
            device_info.set_hardware_version("Zaku-M-V1.7");
#else
            device_info.set_machine_model("Linux-SITL");
            device_info.set_hardware_version("小新Pro 14");
#endif
            char cmd[] = {"uname -sr"};
            char ver[50] = {0};
            int ret = sys_shell_result_to_char(ver, sizeof(ver), cmd);

            if(ret)
            {
                device_info.set_system_version("Unknow");
            }
            else
            {
                device_info.set_system_version(ver);
            }

            device_info.set_software_version(GUNDAM_VISION);
            // 系统状态信息 - 使用可用信息
            float temp = 0.0f;
            std::ifstream temp_file("/sys/class/thermal/thermal_zone0/temp");

            if(temp_file)
            {
                int temp_milliC = 0;
                temp_file >> temp_milliC;
                temp = temp_milliC / 1000.0f; // 转换为摄氏度
            }

            device_info.set_temperature(temp);
            // 获取内存信息
            uint64_t total_mem = 0, free_mem = 0;
            std::ifstream meminfo("/proc/meminfo");

            if(meminfo)
            {
                std::string line;

                while(std::getline(meminfo, line))
                {
                    if(line.find("MemTotal:") == 0)
                    {
                        std::istringstream iss(line);
                        std::string dummy;
                        iss >> dummy >> total_mem;
                    }
                    else if(line.find("MemFree:") == 0)
                    {
                        std::istringstream iss(line);
                        std::string dummy;
                        iss >> dummy >> free_mem;
                    }
                }
            }

            device_info.set_total_memory(total_mem);
            device_info.set_free_memory(free_mem);
            // 设置日志文件信息
            device_info.set_log_file_size(log_file_size);
            device_info.set_log_file_name(chip_id + "_" + datetime + ".ulg");
            device_info.set_datetime(datetime);
            device_info.set_file_checksum(file_checksum);
            // 序列化消息
            std::string serialized_info;

            if(!device_info.SerializeToString(&serialized_info))
            {
                PX4_ERR("序列化设备信息失败");
                return false;
            }

            // 计算信息所属分区
            int32_t info_partition = get_partition_for_chip_id(chip_id);
            // 获取当前毫秒时间戳
            struct timeval tv;
            gettimeofday(&tv, NULL);
            int64_t timestamp_ms = (int64_t)tv.tv_sec * 1000 + (int64_t)tv.tv_usec / 1000;
            // 发送设备信息到fd_info主题
            RdKafka::ErrorCode err = producer->produce(
                    "fd_info",                                   // 主题
                    info_partition,                              // 分区
                    RdKafka::Producer::RK_MSG_COPY,              // 复制消息
                    const_cast<char *>(serialized_info.c_str()), // 值
                    serialized_info.size(),                      // 值大小
                    chip_id.c_str(),                             // 键
                    chip_id.size(),                              // 键大小
                    timestamp_ms,                                // 使用显式时间戳
                    nullptr                                      // 不使用消息不透明数据
                                     );

            if(err != RdKafka::ERR_NO_ERROR)
            {
                PX4_ERR("发送设备信息失败: %s", RdKafka::err2str(err).c_str());
                return false;
            }

            PX4_INFO("设备信息已发送到主题 fd_info, 分区 %d", info_partition);
            // 确保设备信息消息已发送
            producer->flush(5000);
            return true;
        }

        LogWriterTCP::LogWriterTCP(size_t buffer_size, const char *ip, const char *port)
            : _buffers
        {
            // We always write larger chunks (orb messages) to the buffer, so the buffer
            // needs to be larger than the minimum write chunk (300 is somewhat arbitrary)

            math::max(buffer_size, _min_write_chunk + 300),
            perf_alloc(PC_ELAPSED, "logger_tcp_write"),
            perf_alloc(PC_ELAPSED, "logger_tcp_fsync"),
            ip,
            port}
        {
            PX4_ERR("Opened buff size : %ld %ld ip: %s : %s", buffer_size, _min_write_chunk + 300, ip, port);
            pthread_mutex_init(&_mtx, nullptr);
            pthread_cond_init(&_cv, nullptr);
        }

        bool LogWriterTCP::init()
        {
            return true;
        }

        LogWriterTCP::~LogWriterTCP()
        {
            if(_buffers._producer)
            {
                delete _buffers._producer;
                _buffers._producer = nullptr;
            }

            if(_buffers._conf)
            {
                delete _buffers._conf;
                _buffers._conf = nullptr;
            }

            pthread_mutex_destroy(&_mtx);
            pthread_cond_destroy(&_cv);
        }

        void LogWriterTCP::start_log(const char *filename)
        {
            // At this point we don't expect the file to be open, but it can happen for very fast consecutive stop & start
            // calls. In that case we wait for the thread to close the file first.
            lock();

            while(_buffers.fd() >= 0)
            {
                unlock();
                system_usleep(5000);
                lock();
            }

            unlock();

            if(_buffers.start_log(filename))
            {
                PX4_INFO("Opened log file: %s", filename);
                notify();
            }
        }

        void LogWriterTCP::stop_log()
        {
            void *read_ptr;
            bool is_part;
            size_t available = _buffers.get_read_ptr(&read_ptr, &is_part);
            PX4_INFO("Thread tcp log stop %d", (int)available);
            _buffers._should_run = false;
            notify();
            _buffers.close_file();
        }

        int LogWriterTCP::thread_start()
        {
            pthread_attr_t thr_attr;
            pthread_attr_init(&thr_attr);
            sched_param param;
            /* low priority, as this is expensive disk I/O */
            param.sched_priority = SCHED_PRIORITY_DEFAULT - 40;
            (void)pthread_attr_setschedparam(&thr_attr, &param);
            pthread_attr_setstacksize(&thr_attr, PX4_STACK_ADJUSTED(1170));
            int ret = pthread_create(&_thread, &thr_attr, &LogWriterTCP::run_helper, this);
            pthread_attr_destroy(&thr_attr);
            return ret;
        }

        void LogWriterTCP::thread_stop()
        {
            // this will terminate the main loop of the writer thread
            _exit_thread = true;
            _buffers._should_run = false;
            void *read_ptr;
            bool is_part;
            size_t available = _buffers.get_read_ptr(&read_ptr, &is_part);
            PX4_WARN("退出线程: %d", (int)available);
            notify();
            // wait for thread to complete
            int ret = pthread_join(_thread, nullptr);

            if(ret)
            {
                PX4_WARN("join failed: %d", ret);
            }
        }

        void *LogWriterTCP::run_helper(void *context)
        {
            px4_prctl(PR_SET_NAME, "log_writer_tcp", px4_getpid());
            reinterpret_cast<LogWriterTCP *>(context)->run();
            return nullptr;
        }

        void LogWriterTCP::run()
        {
            while(!_exit_thread)
            {
                // Outer endless loop
                // Wait for _should_run flag
                while(!_exit_thread)
                {
                    bool start = false;
                    pthread_mutex_lock(&_mtx);
                    pthread_cond_wait(&_cv, &_mtx);
                    start = _buffers._should_run;
                    pthread_mutex_unlock(&_mtx);

                    if(start)
                    {
                        break;
                    }
                }

                if(_exit_thread)
                {
                    break;
                }

                int poll_count = 0;
                int written = 0;
                hrt_abstime last_fsync = hrt_absolute_time();
                pthread_mutex_lock(&_mtx);

                while(true)
                {
                    const hrt_abstime now = hrt_absolute_time();
                    /* call fsync periodically to minimize potential loss of data */
                    const bool call_fsync = ++poll_count >= 100 || now - last_fsync > 1_s;

                    if(call_fsync)
                    {
                        last_fsync = now;
                        poll_count = 0;
                    }

                    /* Check all buffers for available data. Mission log is first to avoid drops */
                    void *read_ptr;
                    bool is_part;
                    size_t available = _buffers.get_read_ptr(&read_ptr, &is_part);
                    // PX4_ERR("write waite (%d) %d", (int)available, _buffers._should_run);

                    /* if sufficient data available or partial read or terminating, write data */
                    if(available >= 1 || is_part || (!_buffers._should_run && available > 0))
                    {
                        pthread_mutex_unlock(&_mtx);
                        written = _buffers.write_to_file(read_ptr, available, call_fsync);
                        /* _buffers.mark_read() requires _mtx to be locked */
                        pthread_mutex_lock(&_mtx);

                        if(written >= 0)
                        {
                            /* subtract bytes written from number in _buffers (count -= written) */
                            _buffers.mark_read(written);

                            if(!_buffers._should_run && written == static_cast<int>(available) && !is_part)
                            {
                                /* Stop only when all data written */
                                _buffers.close_file();
                            }
                        }
                        else
                        {
                            PX4_ERR("write failed (%i)", errno);
                            _buffers._should_run = false;
                            _buffers.close_file();
                        }
                    }
                    else if(call_fsync && _buffers._should_run)
                    {
                        pthread_mutex_unlock(&_mtx);
                        _buffers.fsync();
                        pthread_mutex_lock(&_mtx);
                    }
                    else if(available == 0 && !_buffers._should_run)
                    {
                        _buffers.close_file();
                    }

                    if(_buffers.fd() < 0)
                    {
                        // stop when both files are closed
                        break;
                    }

                    /* Wait for a call to notify(), which indicates new data is available.
                     * Note that at this point there could already be new data available (because of a longer write),
                     * and calling pthread_cond_wait() will still wait for the next notify(). But this is generally
                     * not an issue because notify() is called regularly. */
                    pthread_cond_wait(&_cv, &_mtx);
                }

                // go back to idle
                pthread_mutex_unlock(&_mtx);
            }
        }

        int LogWriterTCP::write_message(void *ptr, size_t size, uint64_t dropout_start)
        {
            if(_need_reliable_transfer)
            {
                int ret;

                // if there's a dropout, write it first (because we might split the message)
                if(dropout_start)
                {
                    while((ret = write(ptr, 0, dropout_start)) == -1)
                    {
                        unlock();
                        notify();
                        px4_usleep(3000);
                        lock();
                    }
                }

                uint8_t *uptr = (uint8_t *)ptr;

                do
                {
                    // Split into several blocks if the data is longer than the write buffer
                    size_t write_size = math::min(size, _buffers.buffer_size());

                    while((ret = write(uptr, write_size, 0)) == -1)
                    {
                        unlock();
                        notify();
                        px4_usleep(3000);
                        lock();
                    }

                    uptr += write_size;
                    size -= write_size;
                } while(size > 0);

                return ret;
            }

            return write(ptr, size, dropout_start);
        }

        int LogWriterTCP::write(void *ptr, size_t size, uint64_t dropout_start)
        {
            if(!is_started())
            {
                return 0;
            }

            // Bytes available to write
            size_t available = _buffers.available();
            size_t dropout_size = 0;

            if(dropout_start)
            {
                dropout_size = sizeof(ulog_message_dropout_s);
            }

            if(size + dropout_size > available)
            {
                // buffer overflow
                return -1;
            }

            if(dropout_start)
            {
                // write dropout msg
                ulog_message_dropout_s dropout_msg;
                dropout_msg.duration = (uint16_t)(hrt_elapsed_time(&dropout_start) / 1000);
                _buffers.write_no_check(&dropout_msg, sizeof(dropout_msg));
            }

            _buffers.write_no_check(ptr, size);
            return 0;
        }

        LogWriterTCP::LogFileBuffer::LogFileBuffer(size_t log_buffer_size, perf_counter_t perf_write,
                perf_counter_t perf_fsync, const char *ip, const char *port)
            : _buffer_size(log_buffer_size), _fd(-1), _buffer(nullptr), _head(0), _count(0), _total_written(0),
              _perf_write(perf_write), _perf_fsync(perf_fsync), _server_ip(ip), _server_port(port)
        {
            _producer = nullptr;
            _conf = nullptr;
            _seq_num = 0;
            total_bytes_sent = 0;
            total_packets_sent = 0;
            failed_packets = 0;
        }

        LogWriterTCP::LogFileBuffer::~LogFileBuffer()
        {
#if defined(DEBUG_TCP_LOCAL_FILE)

            if(_fd >= 0)
            {
                close(_fd);
            }

#endif
            delete[] _buffer;
            perf_free(_perf_write);
            perf_free(_perf_fsync);

            if(_producer)
            {
                delete _producer;
                _producer = nullptr;
            }

            if(_conf)
            {
                delete _conf;
                _conf = nullptr;
            }
        }

        void LogWriterTCP::LogFileBuffer::write_no_check(void *ptr, size_t size)
        {
            size_t n = _buffer_size - _head; // bytes to end of the buffer
            uint8_t *buffer_c = reinterpret_cast<uint8_t *>(ptr);

            if(size > n)
            {
                // Message goes over the end of the buffer
                memcpy(&(_buffer[_head]), buffer_c, n);
                _head = 0;
            }
            else
            {
                n = 0;
            }

            // now: n = bytes already written
            size_t p = size - n; // number of bytes to write
            memcpy(&(_buffer[_head]), &(buffer_c[n]), p);
            _head = (_head + p) % _buffer_size;
            _count += size;
        }

        size_t LogWriterTCP::LogFileBuffer::get_read_ptr(void **ptr, bool *is_part)
        {
            // bytes available to read
            int read_ptr = _head - _count;

            if(read_ptr < 0)
            {
                read_ptr += _buffer_size;
                *ptr = &_buffer[read_ptr];
                *is_part = true;
                return _buffer_size - read_ptr;
            }
            else
            {
                *ptr = &_buffer[read_ptr];
                *is_part = false;
                return _count;
            }
        }

        bool LogWriterTCP::LogFileBuffer::start_log(const char *filename)
        {
            // 1. 初始化: 读取芯片ID
#if defined(__PX4_POSIX_RK3308)
            char chipid[32] = {0};
            RK_read_chip_id(chipid, sizeof(chipid));
            _chip_id = std::string(chipid);
            PX4_INFO("get cuid: %s", _chip_id.c_str());
#else
            // 使用generate_unique_id()函数获取唯一ID
            _chip_id = generate_unique_id();
            PX4_INFO("Generated unique ID: %s", _chip_id.c_str());
#endif
            // 2. 生成当前日期时间
            time_t now;
            struct tm *tm_info;
            time(&now);
            tm_info = localtime(&now);
            char datetime_buf[15];
            strftime(datetime_buf, sizeof(datetime_buf), "%Y%m%d%H%M%S", tm_info);

            // TODO::增加一个随机数去除1970导致的可能性重复名称

            if(strncmp(datetime_buf, "1970", 4) == 0)
            {
                PX4_INFO("1970 start.....open robot:%s", datetime_buf);
                // 生成10位随机数
                std::random_device rd;
                std::mt19937 gen(rd());
                std::uniform_int_distribution<int64_t> dis(0, 9999999999LL); // 使用int64_t类型和LL后缀
                int64_t random_num = dis(gen);
                // 格式化随机数为10位字符串，不足10位前面补0
                char random_str[11];
                snprintf(random_str, sizeof(random_str), "%010ld", random_num);
                // 保留1970和最后一位，中间替换为随机数
                char new_datetime[15];
                snprintf(new_datetime, sizeof(new_datetime), "1970%s%c",
                         random_str, datetime_buf[14]);
                // 更新datetime_buf
                strncpy(datetime_buf, new_datetime, sizeof(datetime_buf));
                PX4_INFO("fix datetime: %s", datetime_buf);
            }

            _datetime = std::string(datetime_buf);
            // 初始化MD5计算
            init_md5_context();
            // 3. 初始化Kafka配置
            std::string errstr;
            _conf = RdKafka::Conf::create(RdKafka::Conf::CONF_GLOBAL);

            // 3.1 设置Kafka服务器连接参数
            if(_conf->set("bootstrap.servers", KAFKA_BROKERS, errstr) != RdKafka::Conf::CONF_OK)
            {
                PX4_ERR("Kafka配置错误(bootstrap.servers): %s", errstr.c_str());
            }

            if(_conf->set("acks", ACKS_CONFIG, errstr) != RdKafka::Conf::CONF_OK)
            {
                PX4_ERR("Kafka配置错误(acks): %s", errstr.c_str());
            }

            if(_conf->set("api.version.request", "true", errstr) != RdKafka::Conf::CONF_OK)
            {
                PX4_ERR("Kafka配置错误(api.version.request): %s", errstr.c_str());
            }

            // 3.2 设置Socket和重连参数
            if(_conf->set("socket.timeout.ms", std::to_string(SOCKET_TIMEOUT_MS), errstr) != RdKafka::Conf::CONF_OK)
            {
                PX4_ERR("Kafka配置错误(socket.timeout.ms): %s", errstr.c_str());
            }

            if(_conf->set("reconnect.backoff.max.ms", std::to_string(RECONNECT_BACKOFF_MAX_MS), errstr) != RdKafka::Conf::CONF_OK)
            {
                PX4_ERR("Kafka配置错误(reconnect.backoff.max.ms): %s", errstr.c_str());
            }

            if(_conf->set("reconnect.backoff.ms", std::to_string(RECONNECT_BACKOFF_MS), errstr) != RdKafka::Conf::CONF_OK)
            {
                PX4_ERR("Kafka配置错误(reconnect.backoff.ms): %s", errstr.c_str());
            }

            // 3.3 设置消息队列参数
            if(_conf->set("queue.buffering.max.messages", std::to_string(QUEUE_BUFFERING_MAX_MESSAGES),
                          errstr) != RdKafka::Conf::CONF_OK)
            {
                PX4_ERR("Kafka配置错误(queue.buffering.max.messages): %s", errstr.c_str());
            }

            if(_conf->set("queue.buffering.max.kbytes", std::to_string(QUEUE_BUFFERING_MAX_KBYTES),
                          errstr) != RdKafka::Conf::CONF_OK)
            {
                PX4_ERR("Kafka配置错误(queue.buffering.max.kbytes): %s", errstr.c_str());
            }

            if(_conf->set("linger.ms", std::to_string(LINGER_MS_CONFIG), errstr) != RdKafka::Conf::CONF_OK)
            {
                PX4_ERR("Kafka配置错误(linger.ms): %s", errstr.c_str());
            }

            if(_conf->set("message.send.max.retries", std::to_string(MESSAGE_SEND_MAX_RETRIES), errstr) != RdKafka::Conf::CONF_OK)
            {
                PX4_ERR("Kafka配置错误(message.send.max.retries): %s", errstr.c_str());
            }

            if(_conf->set("retry.backoff.ms", std::to_string(RETRY_BACKOFF_MS), errstr) != RdKafka::Conf::CONF_OK)
            {
                PX4_ERR("Kafka配置错误(retry.backoff.ms): %s", errstr.c_str());
            }

            if(_conf->set("request.timeout.ms", std::to_string(REQUEST_TIMEOUT_MS), errstr) != RdKafka::Conf::CONF_OK)
            {
                PX4_ERR("Kafka配置错误(request.timeout.ms): %s", errstr.c_str());
            }

            // 3.4 设置传递报告回调
            static DeliveryReportCallback dr_cb;

            if(_conf->set("dr_cb", &dr_cb, errstr) != RdKafka::Conf::CONF_OK)
            {
                PX4_ERR("Kafka配置错误(dr_cb): %s", errstr.c_str());
            }

            // 4. 创建Producer实例
            _producer = RdKafka::Producer::create(_conf, errstr);

            if(!_producer)
            {
                PX4_ERR("创建Kafka生产者失败: %s", errstr.c_str());
                delete _conf;
                _conf = nullptr;
                return false;
            }

            // 5. 根据芯片ID确定主题和分区
            _topic_name = get_topic_for_chip_id(_chip_id);
            _partition = get_partition_for_chip_id(_chip_id);
            PX4_INFO("使用主题: %s, 分区: %d", _topic_name.c_str(), _partition);
            // 6. 重置序列号
            _seq_num = 0;
#if defined(DEBUG_TCP_LOCAL_FILE)
            // 创建目录结构: ./logs/ulog/chip-id/
            char dir_path[256];
            snprintf(dir_path, sizeof(dir_path), "./logs/ulog/%s", _chip_id.c_str());
            // 创建必要的目录层次
            mkdir("./logs", 0755);
            mkdir("./logs/ulog", 0755);
            mkdir(dir_path, 0755);
            // 构建文件完整路径: ./logs/ulog/chip-id/chip-id-datetime.ulg
            char log_filepath[512];
            snprintf(log_filepath, sizeof(log_filepath), "./logs/ulog/%s/%s_%s.ulg",
                     _chip_id.c_str(), _chip_id.c_str(), _datetime.c_str());
            _fd = ::open(log_filepath, O_CREAT | O_WRONLY, PX4_O_MODE_666);

            if(_fd < 0)
            {
                PX4_ERR("Can't open tcp log file %s, errno: %d", log_filepath, errno);
                return false;
            }
            else
            {
                PX4_INFO("Created log file: %s", log_filepath);
            }

#endif

            if(_buffer == nullptr)
            {
                _buffer = new uint8_t[_buffer_size];

                if(_buffer == nullptr)
                {
                    PX4_ERR("Can't create log buffer");
#if defined(DEBUG_TCP_LOCAL_FILE)
                    ::close(_fd);
                    _fd = -1;
#endif
                    return false;
                }
            }

            // Clear buffer and counters
            _head = 0;
            _count = 0;
            _total_written = 0;
            total_bytes_sent = 0;
            total_packets_sent = 0;
            _should_run = true;
            PX4_WARN("日志记录开始: 日志文件：%s_%s.ulg, 主题=%s, 分区=%d",
                     _chip_id.c_str(), _datetime.c_str(), _topic_name.c_str(), _partition);
            return true;
        }

        void LogWriterTCP::LogFileBuffer::fsync() const
        {
            perf_begin(_perf_fsync);
#if defined(DEBUG_TCP_LOCAL_FILE)
            ::fsync(_fd);
#endif
            perf_end(_perf_fsync);
        }

        ssize_t LogWriterTCP::LogFileBuffer::write_to_file(const void *buffer, size_t size, bool call_fsync) const
        {
            // 每500包打印一次详细状态信息
            if(send_count++ % 500 == 0)
            {
                PX4_INFO("发送状态:当前文件: %s_%s.ulg, 包数=%d, 本次大小=%zu字节, 缓冲区已用=%zu/%zu字节(%.1f%%), 已写入=%zu字节",
                         _chip_id.c_str(), _datetime.c_str(), send_count, size,
                         buffer_size() - available(), buffer_size(),
                         (float)(buffer_size() - available()) / buffer_size() * 100.0f,
                         total_written());

                // 如果有Kafka连接，显示Kafka状态
                if(_producer)
                {
                    // 直接显示队列长度而不尝试获取详细统计信息
                    PX4_INFO("消息状态: %d条消息已排队, 序列号=%lu, 总计已发送=%lu字节",
                             _producer->outq_len(), _seq_num, total_bytes_sent);

                    if(consecutive_failures > 0)
                    {
                        const float backoff_seconds = (hrt_absolute_time() - last_success_time) / 1e6f;

                        if(backoff_seconds > 60)
                        {
                            PX4_WARN("网络连接问题: %d秒无成功发送，已限流至%d%%速率",
                                     (int)backoff_seconds, 100 / throttle_factor);
                        }

                        // 检测并警告网络连接问题
                        if(_producer->outq_len() > 0 &&
                                _producer->outq_len() == _seq_num && _seq_num > 1000)
                        {
                            PX4_WARN("严重网络问题：所有消息(%lu条)都在队列中未发送", _seq_num);
                        }
                    }
                }
            }

            if(throttle_factor > 1 && (send_count % throttle_factor != 0))
            {
                _seq_num++;
                total_packets_sent++;
                total_bytes_sent += size;
                return size;
            }

            // 简单的缓冲区聚合策略：只有当缓冲区数据足够多时才发送
            const size_t current_buffered = buffer_size() - available();
            const size_t min_send_threshold = BATCH_TRIGGER_SIZE; // 使用配置的阈值
            // 检查是否应该发送：
            // 1. 缓冲区数据达到阈值
            // 2. 日志即将结束 (!_should_run)
            // 3. 定期强制发送 (call_fsync)
            bool should_send = (current_buffered >= min_send_threshold) ||
                               (!_should_run) ||
                               (call_fsync && current_buffered > 0);

            if(!should_send || !_producer)
            {
                // 不发送，直接返回
                return size;
            }

            // 发送当前缓冲区的所有数据
            void *read_ptr;
            bool is_part;
            size_t available_data = const_cast<LogFileBuffer *>(this)->get_read_ptr(&read_ptr, &is_part);

            if(available_data == 0)
            {
                return size;
            }

            // 使用protobuf和Kafka发送数据
            const uint8_t *data = static_cast<const uint8_t *>(read_ptr);
            // 更新MD5计算
            update_md5_context(data, available_data);
            PX4_INFO("发送聚合数据 - 序列号:%lu, 聚合大小:%zu字节", _seq_num, available_data);
            // 创建protobuf消息
            binary_stream::StreamData stream_data;
            stream_data.set_sequence_number(_seq_num);
            stream_data.set_payload(data, available_data);
            stream_data.set_chip_id(_chip_id);
            stream_data.set_checksum(calculate_checksum(data, available_data));
            stream_data.set_payload_length(available_data);
            // 设置时间戳
            struct timeval tv;
            gettimeofday(&tv, NULL);
            int64_t timestamp_ms = (int64_t)tv.tv_sec * 1000 + (int64_t)tv.tv_usec / 1000;
            stream_data.set_timestamp(timestamp_ms);
            stream_data.set_is_last_frame(false); // 正常情况下不是最后一帧
            stream_data.set_datetime(_datetime);
            // 序列化消息
            std::string serialized_data;

            if(!stream_data.SerializeToString(&serialized_data))
            {
                PX4_ERR("序列化消息失败");
                return -1;
            }

            // PX4_INFO("序列化数据 - 序列号:%lu, 原始大小:%zu, 序列化大小:%zu, 时间戳：%ld",
            //          _seq_num, size, serialized_data.size(), timestamp_ms);
            // 发送数据到Kafka，添加重试逻辑
            int retry_count = 0;
            const int MAX_RETRY = 3;
            RdKafka::ErrorCode err;

            while(retry_count < MAX_RETRY)
            {
                err = _producer->produce(
                                      _topic_name,
                                      _partition,                                  // 使用计算得到的固定分区
                                      RdKafka::Producer::RK_MSG_COPY,              // 复制消息
                                      const_cast<char *>(serialized_data.c_str()), // 值
                                      serialized_data.size(),                      // 值大小
                                      _chip_id.c_str(),                            // 键
                                      _chip_id.size(),                             // 键大小
                                      timestamp_ms,                                // 使用显式时间戳
                                      nullptr                                      // 不使用消息不透明数据
                      );

                if(err == RdKafka::ERR_NO_ERROR)
                {
                    break;
                }
                else if(err == RdKafka::ERR__QUEUE_FULL)
                {
                    PX4_WARN("消息队列已满(%d条)，暂停发送，等待消息处理...尝试 %d/%d",
                             _producer->outq_len(), retry_count + 1, MAX_RETRY);

                    for(int i = 0; i < 5; i++)
                    {
                        _producer->poll(100);
                        usleep(20000);
                    }

                    retry_count++;
                }
                else
                {
                    PX4_ERR("发送数据到主题 %s 失败: %s (尝试 %d/%d)",
                            _topic_name.c_str(), RdKafka::err2str(err).c_str(),
                            retry_count + 1, MAX_RETRY);
                    retry_count++;
                    usleep(50000); // 短暂等待后重试
                }
            }

            if(err != RdKafka::ERR_NO_ERROR && retry_count >= MAX_RETRY)
            {
                return -1;
            }

            // 增加序列号
            _seq_num++;
            total_packets_sent++;
            total_bytes_sent += available_data;
            // 标记数据为已读
            const_cast<LogFileBuffer *>(this)->mark_read(available_data);

            // 更频繁地进行poll，确保消息回调被处理
            if(_seq_num % 20 == 0)
            {
                _producer->poll(0);
            }

            // 及时处理队列积压
            if(_producer->outq_len() > 5000)
            {
                if(_producer->outq_len() > 10000)
                {
                    PX4_WARN("队列积压严重(%d)，强制处理中...", _producer->outq_len());
                    _producer->poll(250);
                }
                else
                {
                    _producer->poll(50);
                }
            }

#if defined(DEBUG_TCP_LOCAL_FILE)
            perf_begin(_perf_write);
            ssize_t ret = ::write(_fd, buffer, size);
            perf_end(_perf_write);

            if(call_fsync)
            {
                fsync();
            }

            return ret;
#else
            return size;
#endif
        }

        void LogWriterTCP::LogFileBuffer::close_file()
        {
            // 在关闭前发送剩余的缓冲区数据
            if(_producer)
            {
                void *read_ptr;
                bool is_part;
                size_t available_data = get_read_ptr(&read_ptr, &is_part);

                if(available_data > 0)
                {
                    PX4_INFO("发送剩余缓冲区数据: %zu字节", available_data);
                    // 使用protobuf和Kafka发送剩余数据
                    const uint8_t *data = static_cast<const uint8_t *>(read_ptr);
                    update_md5_context(data, available_data);
                    binary_stream::StreamData stream_data;
                    stream_data.set_sequence_number(_seq_num);
                    stream_data.set_payload(data, available_data);
                    stream_data.set_chip_id(_chip_id);
                    stream_data.set_checksum(calculate_checksum(data, available_data));
                    stream_data.set_payload_length(available_data);
                    struct timeval tv;
                    gettimeofday(&tv, NULL);
                    int64_t timestamp_ms = (int64_t)tv.tv_sec * 1000 + (int64_t)tv.tv_usec / 1000;
                    stream_data.set_timestamp(timestamp_ms);
                    stream_data.set_is_last_frame(false);
                    stream_data.set_datetime(_datetime);
                    std::string serialized_data;

                    if(stream_data.SerializeToString(&serialized_data))
                    {
                        _producer->produce(
                                        _topic_name,
                                        _partition,
                                        RdKafka::Producer::RK_MSG_COPY,
                                        const_cast<char *>(serialized_data.c_str()),
                                        serialized_data.size(),
                                        _chip_id.c_str(),
                                        _chip_id.size(),
                                        timestamp_ms,
                                        nullptr
                        );
                        _seq_num++;
                        total_packets_sent++;
                        total_bytes_sent += available_data;
                        mark_read(available_data);
                    }
                }
            }

            // 打印最终统计信息
            PX4_INFO("日志发送统计:当前文件: %s_%s.ulg, 总包数=%lu, 成功=%lu, 失败=%lu, 总字节数=%lu",
                     _chip_id.c_str(), _datetime.c_str(), total_packets_sent, total_packets_sent - failed_packets,
                     failed_packets, total_bytes_sent);

            if(failed_packets > 0)
            {
                PX4_INFO("传输成功率: %.1f%%, 丢包率: %.1f%%",
                         (float)(total_packets_sent - failed_packets) / total_packets_sent * 100.0f,
                         (float)failed_packets / total_packets_sent * 100.0f);
            }

            // 完成MD5计算并输出校验和
            std::string md5_sum;

            if(md5_initialized)
            {
                md5_sum = finalize_md5_context();
                PX4_INFO("数据MD5校验和: %s", md5_sum.c_str());
            }

            // 发送最后一个包标记流结束
            if(_producer)
            {
                PX4_INFO("准备发送最后一帧标记，序列号=%lu", _seq_num);

                // 先尝试处理队列中的消息
                if(_producer->outq_len() > 0)
                {
                    PX4_INFO("在发送结束标记前清空队列(%d条消息)...", _producer->outq_len());
                    // 分批次处理
                    int retry = 0;

                    while(_producer->outq_len() > 0 && retry < 50)
                    {
                        retry++;
                        int msgs_before = _producer->outq_len();
                        PX4_INFO("正在等待消息队列清空，尝试 %d/50 (队列中还有 %d 条消息)",
                                 retry, msgs_before);
                        _producer->flush(5000);

                        // 如果队列没有减少，等待一下再试
                        if(_producer->outq_len() == msgs_before && msgs_before > 0)
                        {
                            usleep(100000);
                        }

                        // 如果队列为空或者基本清空，就退出
                        if(_producer->outq_len() == 0 ||
                                (msgs_before > 100 && _producer->outq_len() < 10))
                        {
                            break;
                        }
                    }
                }

                PX4_INFO("发送最后一帧标记，序列号=%lu", _seq_num);
                binary_stream::StreamData stream_data;
                stream_data.set_sequence_number(_seq_num);
                stream_data.set_payload(nullptr, 0);
                stream_data.set_chip_id(_chip_id);
                stream_data.set_checksum(0);
                stream_data.set_payload_length(0);
                struct timeval tv;
                gettimeofday(&tv, NULL);
                int64_t timestamp_ms = (int64_t)tv.tv_sec * 1000 + (int64_t)tv.tv_usec / 1000;
                stream_data.set_timestamp(timestamp_ms);
                stream_data.set_is_last_frame(true);
                stream_data.set_datetime(_datetime);
                std::string serialized_data;

                if(stream_data.SerializeToString(&serialized_data))
                {
                    PX4_INFO("结束标记已序列化 - 大小:%zu", serialized_data.size());
                    _producer->produce(
                                    _topic_name,
                                    _partition,
                                    RdKafka::Producer::RK_MSG_COPY,
                                    const_cast<char *>(serialized_data.c_str()),
                                    serialized_data.size(),
                                    _chip_id.c_str(),
                                    _chip_id.size(),
                                    timestamp_ms, // 使用之前获取的时间戳
                                    nullptr);
                }

                // 强制刷新消息队列
                PX4_INFO("等待最终消息队列清空...");
                _producer->flush(30000);
                PX4_INFO("消息队列已清空，关闭生产者. 队列状态 - 剩余消息:%d",
                         _producer->outq_len());
                // 发送设备信息到fd_info主题
                send_device_info(_producer, _chip_id, _datetime, total_bytes_sent, md5_sum);
                // 清理资源
                delete _producer;
                _producer = nullptr;

                if(_conf)
                {
                    delete _conf;
                    _conf = nullptr;
                }
            }

            _head = 0;
            _count = 0;
#if defined(DEBUG_TCP_LOCAL_FILE)

            if(_fd >= 0)
            {
                int res = close(_fd);
                _fd = -1;

                if(res)
                {
                    PX4_WARN("closing log file failed (%i)", errno);
                }
                else
                {
                    PX4_INFO("closed logfile, bytes written: %zu", _total_written);
                }
            }

#else
            PX4_INFO("closed Kafka stream, 序列号: %lu, 字节数: %zu", _seq_num, _total_written);
#endif
        }
    } // namespace logger
} // namespace px4
